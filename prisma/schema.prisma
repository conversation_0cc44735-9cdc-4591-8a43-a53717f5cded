// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "./generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id          String         @id @default(cuid())
  name        String
  description String
  image       String
  order       Int            @default(autoincrement())
  basePrice   Float
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  sizes       Size[]
  extras      Extra[]
}

enum ProductSize {
  SMALL
  MEDIUM
  LARGE
}

model Size{
  id          String         @id @default(cuid())
  name        ProductSize
  Product     Product        @relation(fields: [productId], references: [id])
  productId   String
  price       Float
}

enum ExtraIngredients {
CHEESE
BACON
TOMATOES
ONIONS
PEPPERS
PEPPERONI
SAUSAGE
MUSHROOMS
OLIVES
}

model Extra{
  id          String         @id @default(cuid())
  name        ExtraIngredients
  Product     Product        @relation(fields: [productId], references: [id])
  productId   String
  price       Float
}