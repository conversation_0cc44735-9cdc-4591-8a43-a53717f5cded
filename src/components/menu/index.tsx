import { Product, Size, Extra } from "../../../prisma/generated/prisma";
import MenuItem from "./MenuItem";

// Define the type for Product with included relations
type ProductWithRelations = Product & {
    sizes: Size[];
    extras: Extra[];
};

const Menu = ({ items }: { items: ProductWithRelations[] }) => {

    // console.log(items[0].sizes)

    return (
        <ul className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            {items.map((item: ProductWithRelations) => (
                <MenuItem key={item.id} item={item} />
            ))}
        </ul>
    )
}

export default Menu;
